import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl;

  // 检查是否为静态资源（移除调试日志提升性能）
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/images/') ||
    pathname.startsWith('/fonts/') ||
    pathname.startsWith('/videos/') ||
    pathname.includes('.') ||
    pathname.includes('favicon')
  ) {
    return NextResponse.next();
  }

  // 从 cookies 中获取 token
  const token = request.cookies.get('auth-token')?.value;

  // 需要认证的路径
  const protectedPaths = ['/dashboard'];

  // 公共路径（不需要认证）
  const publicPaths = ['/', '/signin', '/signup'];

  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  const isPublicPath = publicPaths.includes(pathname);

  // 如果是受保护的路径但没有 token，重定向到登录页（保留查询参数）
  if (isProtectedPath && !token) {
    const redirectUrl = new URL('/signin', request.url);
    // 保留原始URL作为回调参数
    if (pathname !== '/signin') {
      redirectUrl.searchParams.set('callbackUrl', pathname + search);
    }
    return NextResponse.redirect(redirectUrl);
  }

  // 如果有 token 但访问登录页，重定向到仪表板（保留查询参数）
  if (token && (pathname === '/signin' || pathname === '/signup')) {
    const redirectUrl = new URL('/dashboard/tasks', request.url);
    // 如果有回调URL，使用回调URL
    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');
    if (callbackUrl && callbackUrl.startsWith('/')) {
      return NextResponse.redirect(new URL(callbackUrl, request.url));
    }
    return NextResponse.redirect(redirectUrl);
  }

  // 优化：直接重定向 /dashboard 到 /dashboard/tasks，避免二次重定向（保留查询参数）
  if (pathname === '/dashboard' && token) {
    const redirectUrl = new URL('/dashboard/tasks', request.url);
    redirectUrl.search = search; // 保留查询参数
    return NextResponse.redirect(redirectUrl);
  }

  // 调用国际化中间件处理其他情况
  const intlMiddleware = createIntlMiddleware(routing);
  return intlMiddleware(request);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, fonts, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)',
  ],
};