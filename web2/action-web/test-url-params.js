#!/usr/bin/env node

const http = require('http');

// 测试不同的URL路径
const testUrls = [
  'http://localhost:3000/',
  'http://localhost:3000/?test=123',
  'http://localhost:3000/dashboard',
  'http://localhost:3000/dashboard?param=value',
  'http://localhost:3000/dashboard/tasks',
  'http://localhost:3000/dashboard/tasks?filter=active',
];

async function testUrl(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          url,
          statusCode: res.statusCode,
          headers: res.headers,
          contentLength: data.length,
          hasRedirect: res.statusCode >= 300 && res.statusCode < 400,
          location: res.headers.location || null
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function runTests() {
  console.log('🧪 测试URL参数处理...\n');
  
  for (const url of testUrls) {
    try {
      const result = await testUrl(url);
      console.log(`✅ ${result.url}`);
      console.log(`   状态码: ${result.statusCode}`);
      if (result.hasRedirect) {
        console.log(`   重定向到: ${result.location}`);
      }
      console.log(`   内容长度: ${result.contentLength} bytes`);
      console.log('');
    } catch (error) {
      console.log(`❌ ${url}`);
      console.log(`   错误: ${error.message}`);
      console.log('');
    }
  }
}

runTests().catch(console.error);
